<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>订单表单测试</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-gray-100 p-8">
  <div class="max-w-4xl mx-auto">
    <h1 class="text-3xl font-bold mb-8 text-center">订单表单重构测试</h1>
    
    <!-- 订单表单视图 -->
    <div id="order-form-view" class="view-content">
      <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex justify-between items-center mb-6">
          <h1 id="order-form-title" class="text-2xl font-bold text-gray-900">新建订单</h1>
          <div class="flex space-x-3">
            <button id="save-order-btn" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
              保存订单
            </button>
            <button id="cancel-order-btn" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500">
              取消
            </button>
          </div>
        </div>

        <!-- 订单基本信息 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label for="order-date" class="block text-sm font-medium text-gray-700 mb-2">订单日期 *</label>
            <input type="date" id="order-date" class="form-control" required>
          </div>
          <div>
            <label for="customer-name" class="block text-sm font-medium text-gray-700 mb-2">客户名称 *</label>
            <input type="text" id="customer-name" class="form-control" placeholder="请输入客户名称" required>
          </div>
          <div>
            <label for="customer-contact" class="block text-sm font-medium text-gray-700 mb-2">客户联系方式</label>
            <input type="text" id="customer-contact" class="form-control" placeholder="请输入客户联系方式">
          </div>
          <div>
            <label for="intermediary-name" class="block text-sm font-medium text-gray-700 mb-2">中间商名称</label>
            <input type="text" id="intermediary-name" class="form-control" placeholder="请输入中间商名称">
          </div>
          <div>
            <label for="intermediary-contact" class="block text-sm font-medium text-gray-700 mb-2">中间商联系方式</label>
            <input type="text" id="intermediary-contact" class="form-control" placeholder="请输入中间商联系方式">
          </div>
        </div>

        <!-- 材料项列表 -->
        <div class="mb-6">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-semibold text-gray-900">材料清单</h2>
            <button id="add-item-btn" type="button" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
              添加材料项
            </button>
          </div>

          <!-- 材料项表头 -->
          <div class="grid grid-cols-12 gap-4 mb-3 text-sm font-medium text-gray-700 border-b border-gray-200 pb-2">
            <div class="col-span-5">材料名称</div>
            <div class="col-span-2 text-center">数量</div>
            <div class="col-span-2 text-center">单价</div>
            <div class="col-span-2 text-center">金额</div>
            <div class="col-span-1 text-center">操作</div>
          </div>

          <!-- 材料项容器 -->
          <div id="order-items-container" class="space-y-3">
            <!-- 材料项将通过JavaScript动态添加 -->
          </div>
        </div>

        <!-- 订单备注 -->
        <div class="mb-6">
          <label for="order-notes" class="block text-sm font-medium text-gray-700 mb-2">订单备注</label>
          <textarea id="order-notes" rows="3" class="form-control" placeholder="请输入订单备注信息"></textarea>
        </div>

        <!-- 订单总计 -->
        <div class="border-t border-gray-200 pt-4">
          <div class="flex justify-between items-center">
            <span class="text-lg font-semibold text-gray-900">订单总金额：</span>
            <span id="order-total-amount" class="text-2xl font-bold text-indigo-600">¥0.00</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 简化的测试脚本 -->
  <script>
    // 模拟必要的全局对象
    window.stateManager = {
      getState: () => null,
      setState: () => {},
      setLoading: () => {}
    };
    
    window.orderService = {
      validateOrderData: () => ({ isValid: true, errors: [] }),
      createOrder: () => Promise.resolve({ success: true }),
      updateOrder: () => Promise.resolve({ success: true })
    };

    // 设置默认日期
    document.getElementById('order-date').value = new Date().toISOString().split('T')[0];

    // 添加材料项功能
    function addOrderItem() {
      const container = document.getElementById('order-items-container');
      if (!container) return;

      const itemRow = document.createElement('div');
      itemRow.className = 'grid grid-cols-12 gap-4 mb-3 items-center p-3 bg-gray-50 rounded-lg border border-gray-200';

      itemRow.innerHTML = `
        <div class="col-span-5">
          <input type="text" class="form-control item-name" placeholder="输入材料名称" required>
        </div>
        <div class="col-span-2">
          <input type="number" class="form-control item-quantity text-center" value="1" min="0" step="0.01" required>
        </div>
        <div class="col-span-2">
          <input type="number" class="form-control item-price text-center" value="0" min="0" step="0.01" required>
        </div>
        <div class="col-span-2 text-right">
          <span class="item-amount text-lg font-semibold text-gray-900">¥0.00</span>
        </div>
        <div class="col-span-1 text-center">
          <button type="button" class="delete-item p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      `;

      container.appendChild(itemRow);

      // 添加事件监听器
      const quantityInput = itemRow.querySelector('.item-quantity');
      const priceInput = itemRow.querySelector('.item-price');
      const deleteButton = itemRow.querySelector('.delete-item');

      quantityInput.addEventListener('input', () => calculateItemAmount(itemRow));
      priceInput.addEventListener('input', () => calculateItemAmount(itemRow));

      deleteButton.addEventListener('click', () => {
        if (container.children.length > 1) {
          itemRow.remove();
          calculateOrderTotal();
        } else {
          alert('订单至少需要一个材料项');
        }
      });

      calculateItemAmount(itemRow);
    }

    function calculateItemAmount(row) {
      const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
      const price = parseFloat(row.querySelector('.item-price').value) || 0;
      const amount = quantity * price;

      row.querySelector('.item-amount').textContent = `¥${amount.toFixed(2)}`;
      calculateOrderTotal();
    }

    function calculateOrderTotal() {
      let total = 0;
      document.querySelectorAll('#order-items-container .item-amount').forEach(element => {
        const amountText = element.textContent;
        const amount = parseFloat(amountText.replace('¥', '')) || 0;
        total += amount;
      });

      document.getElementById('order-total-amount').textContent = `¥${total.toFixed(2)}`;
    }

    // 绑定事件
    document.getElementById('add-item-btn').addEventListener('click', addOrderItem);
    document.getElementById('save-order-btn').addEventListener('click', () => {
      alert('保存功能测试 - 表单验证和数据收集正常');
    });
    document.getElementById('cancel-order-btn').addEventListener('click', () => {
      alert('取消功能测试');
    });

    // 初始化一个材料项
    addOrderItem();
  </script>
</body>
</html>
