<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>MatBook 建材店铺记账</title>
  <!-- Tailwind CSS 通过 CDN 引入 -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Chart.js 通过 CDN 引入 -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-gray-100">
  <!-- 登录视图 -->
  <div id="login-view" class="hidden min-h-screen flex items-center justify-center">
    <div class="bg-white p-8 rounded-lg shadow-md w-96">
      <h1 class="text-2xl font-bold mb-6 text-center">MatBook 建材店铺记账</h1>
      <form id="login-form" class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700">用户名</label>
          <input type="text" id="username" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">密码</label>
          <input type="password" id="password" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
        </div>
        <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
          登录
        </button>
      </form>
    </div>
  </div>

  <!-- 主应用视图 -->
  <div id="app-view" class="min-h-screen flex flex-col">
    <!-- 导航栏 -->
    <nav class="bg-indigo-600 text-white shadow-md">
      <div class="max-w-7xl mx-auto px-4">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <span class="text-xl font-bold">MatBook</span>
          </div>
          <div class="flex items-center space-x-4">
            <button id="nav-dashboard" class="nav-btn px-3 py-2 rounded-md text-sm font-medium hover:bg-indigo-500">仪表盘</button>
            <button id="nav-new-order" class="nav-btn px-3 py-2 rounded-md text-sm font-medium hover:bg-indigo-500" onclick="handleNewOrderClick()">新建订单</button>
            <button id="nav-order-list" class="nav-btn px-3 py-2 rounded-md text-sm font-medium hover:bg-indigo-500">订单列表</button>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="flex-1 max-w-7xl mx-auto px-4 py-6">
      <!-- 仪表盘视图 -->
      <div id="dashboard-view" class="view-content">
        <h1 class="text-2xl font-bold mb-6">仪表盘</h1>

        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-lg font-semibold text-gray-700">今日订单数</h2>
            <p id="today-order-count" class="text-3xl font-bold text-indigo-600">0</p>
          </div>
          <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-lg font-semibold text-gray-700">今日总金额</h2>
            <p id="today-total-amount" class="text-3xl font-bold text-indigo-600">¥0.00</p>
          </div>
          <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-lg font-semibold text-gray-700">总订单数</h2>
            <p id="total-order-count" class="text-3xl font-bold text-indigo-600">0</p>
          </div>
        </div>

        <!-- 图表 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 class="text-lg font-semibold text-gray-700 mb-4">订单趋势</h2>
          <canvas id="orders-chart" height="200"></canvas>
        </div>

        <!-- 最近订单 -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-semibold text-gray-700">最近订单</h2>
            <button id="create-order-btn" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
              创建新订单
            </button>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">订单ID</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">日期</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">客户</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">金额</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody id="recent-orders-table" class="bg-white divide-y divide-gray-200">
                <!-- 最近订单数据将通过JavaScript动态填充 -->
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- 订单表单视图 -->
      <div id="order-form-view" class="view-content hidden">
        <div class="bg-white rounded-lg shadow-md p-6">
          <div class="flex justify-between items-center mb-6">
            <h1 id="order-form-title" class="text-2xl font-bold text-gray-900">新建订单</h1>
            <div class="flex space-x-3">
              <button id="save-order-btn" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                保存订单
              </button>
              <button id="cancel-order-btn" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                取消
              </button>
            </div>
          </div>

          <!-- 订单基本信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label for="order-date" class="block text-sm font-medium text-gray-700 mb-2">订单日期 *</label>
              <input type="date" id="order-date" class="form-control" required>
            </div>
            <div>
              <label for="customer-name" class="block text-sm font-medium text-gray-700 mb-2">客户名称 *</label>
              <input type="text" id="customer-name" class="form-control" placeholder="请输入客户名称" required>
            </div>
            <div>
              <label for="customer-contact" class="block text-sm font-medium text-gray-700 mb-2">客户联系方式</label>
              <input type="text" id="customer-contact" class="form-control" placeholder="请输入客户联系方式">
            </div>
            <div>
              <label for="intermediary-name" class="block text-sm font-medium text-gray-700 mb-2">中间商名称</label>
              <input type="text" id="intermediary-name" class="form-control" placeholder="请输入中间商名称">
            </div>
            <div>
              <label for="intermediary-contact" class="block text-sm font-medium text-gray-700 mb-2">中间商联系方式</label>
              <input type="text" id="intermediary-contact" class="form-control" placeholder="请输入中间商联系方式">
            </div>
          </div>

          <!-- 材料项列表 -->
          <div class="mb-6">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-lg font-semibold text-gray-900">材料清单</h2>
              <button id="add-item-btn" type="button" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                添加材料项
              </button>
            </div>

            <!-- 材料项表头 -->
            <div class="grid grid-cols-12 gap-4 mb-3 text-sm font-medium text-gray-700 border-b border-gray-200 pb-2">
              <div class="col-span-5">材料名称</div>
              <div class="col-span-2 text-center">数量</div>
              <div class="col-span-2 text-center">单价</div>
              <div class="col-span-2 text-center">金额</div>
              <div class="col-span-1 text-center">操作</div>
            </div>

            <!-- 材料项容器 -->
            <div id="order-items-container" class="space-y-3">
              <!-- 材料项将通过JavaScript动态添加 -->
            </div>
          </div>

          <!-- 订单备注 -->
          <div class="mb-6">
            <label for="order-notes" class="block text-sm font-medium text-gray-700 mb-2">订单备注</label>
            <textarea id="order-notes" rows="3" class="form-control" placeholder="请输入订单备注信息"></textarea>
          </div>

          <!-- 订单总计 -->
          <div class="border-t border-gray-200 pt-4">
            <div class="flex justify-between items-center">
              <span class="text-lg font-semibold text-gray-900">订单总金额：</span>
              <span id="order-total-amount" class="text-2xl font-bold text-indigo-600">¥0.00</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 订单列表视图 -->
      <div id="order-list-view" class="view-content hidden">
        <h1 class="text-2xl font-bold mb-6">订单列表</h1>

        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-4">
            <div>
              <label class="block text-sm font-medium text-gray-700">关键词搜索</label>
              <input type="text" id="search-keyword" placeholder="客户名称或中间商名称" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">开始日期</label>
              <input type="date" id="search-start-date" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">结束日期</label>
              <input type="date" id="search-end-date" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
            </div>
          </div>

          <div class="flex justify-between">
            <button id="search-btn" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
              搜索
            </button>
            <button id="export-csv-btn" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
              导出CSV
            </button>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">订单ID</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">日期</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">客户名称</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">中间商名称</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">总金额</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody id="orders-table" class="bg-white divide-y divide-gray-200">
                <!-- 订单数据将通过JavaScript动态填充 -->
              </tbody>
            </table>
          </div>

          <!-- 分页控件 -->
          <div class="flex justify-between items-center mt-4">
            <div>
              <span id="pagination-info">显示 1-10 条，共 0 条</span>
            </div>
            <div class="flex space-x-2">
              <button id="prev-page-btn" class="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50">上一页</button>
              <button id="next-page-btn" class="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50">下一页</button>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>

  <!-- 脚本 -->
  <!-- 工具类 -->
  <script src="src/renderer/utils/StateManager.js"></script>
  <script src="src/renderer/utils/ViewManager.js"></script>

  <!-- 服务层 -->
  <script src="src/renderer/services/OrderService.js"></script>

  <!-- 主应用脚本 -->
  <script src="app.js"></script>

  <!-- 测试脚本（开发环境） -->
  <script src="test-refactored-app.js"></script>
</body>
</html>

