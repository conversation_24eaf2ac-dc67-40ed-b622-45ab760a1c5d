/* 确保 hidden 类正确定义 */
.hidden {
  display: none !important;
}

/* 确保视图内容有基本样式 */
.view-content {
  width: 100%;
  padding: 1rem;
}

.nav-btn.active {
  background-color: rgba(79, 70, 229, 0.8);
}

/* 表单验证样式 */
input:invalid, textarea:invalid {
  border-color: #ef4444;
}

.error-message {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 订单表单样式优化 */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #374151;
}

.form-control {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.25);
  outline: none;
}

textarea.form-control {
  min-height: 100px;
  resize: vertical;
}

.form-optional {
  color: #9ca3af;
  font-size: 0.875rem;
  font-weight: normal;
  margin-left: 0.25rem;
}

/* 信息卡片样式 */
.info-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.info-card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

/* 材料项目样式 */
.materials-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.materials-card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
}

.material-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  gap: 0.75rem;
}

.material-item-input {
  flex: 3;
}

.material-item-quantity,
.material-item-price {
  flex: 1;
}

.material-item-amount {
  flex: 1;
  text-align: right;
  font-weight: 500;
}

.material-item-delete {
  flex: 0 0 auto;
}

.add-material-btn {
  width: 100%;
  padding: 0.75rem;
  background-color: #f9fafb;
  border: 1px dashed #d1d5db;
  border-radius: 0.375rem;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.add-material-btn:hover {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

.total-amount {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 1rem;
  font-weight: 600;
}

.total-amount-label {
  margin-right: 1rem;
  color: #4b5563;
}

.total-amount-value {
  font-size: 1.25rem;
  color: #1f2937;
}

/* 订单表单特定样式 */
.order-form-container {
  max-width: 1200px;
  margin: 0 auto;
}

.order-form-section {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.order-form-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #e5e7eb;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.form-field {
  display: flex;
  flex-direction: column;
}

.form-field label {
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.form-field .required::after {
  content: ' *';
  color: #ef4444;
}

.form-control.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-control.success {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* 材料项样式改进 */
.material-items-header {
  display: grid;
  grid-template-columns: 5fr 2fr 2fr 2fr 1fr;
  gap: 1rem;
  padding: 0.75rem 1rem;
  background-color: #f9fafb;
  border-radius: 0.375rem;
  font-weight: 500;
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.material-item-row {
  display: grid;
  grid-template-columns: 5fr 2fr 2fr 2fr 1fr;
  gap: 1rem;
  align-items: center;
  padding: 1rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  margin-bottom: 0.75rem;
  transition: all 0.2s ease;
}

.material-item-row:hover {
  background-color: #f3f4f6;
  border-color: #d1d5db;
}

.material-item-amount {
  font-weight: 600;
  color: #1f2937;
  text-align: right;
}

.delete-item-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: transparent;
  border: none;
  color: #ef4444;
  cursor: pointer;
  transition: all 0.2s ease;
}

.delete-item-btn:hover {
  background-color: #fef2f2;
  color: #dc2626;
}

/* 按钮样式改进 */
.btn-primary {
  background-color: #4f46e5;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  border: none;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary:hover:not(:disabled) {
  background-color: #4338ca;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-secondary {
  background-color: white;
  color: #374151;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  border: 1px solid #d1d5db;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.btn-success {
  background-color: #10b981;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  border: none;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-success:hover {
  background-color: #059669;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* 总金额显示样式 */
.order-total-section {
  border-top: 2px solid #e5e7eb;
  padding-top: 1.5rem;
  margin-top: 1.5rem;
}

.order-total-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #f8fafc;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
}

.order-total-label {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
}

.order-total-amount {
  font-size: 1.875rem;
  font-weight: 700;
  color: #4f46e5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }

  .material-items-header,
  .material-item-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .material-items-header {
    display: none; /* 在移动设备上隐藏表头 */
  }

  .order-total-display {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
}



