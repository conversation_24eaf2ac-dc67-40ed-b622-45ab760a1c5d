/**
 * 重构后的应用程序入口
 * 使用新的架构：StateManager, ViewManager, OrderService
 */

// 等待所有脚本加载完成
document.addEventListener('DOMContentLoaded', async function() {
  console.log('应用程序开始初始化...');

  try {
    // 检查必要的服务是否已加载
    if (!window.stateManager) {
      throw new Error('StateManager 未加载');
    }
    if (!window.viewManager) {
      throw new Error('ViewManager 未加载');
    }
    if (!window.orderService) {
      throw new Error('OrderService 未加载');
    }
    if (!window.electronAPI) {
      throw new Error('ElectronAPI 未加载');
    }

    console.log('所有服务已加载，开始初始化应用...');

    // 初始化应用
    await initializeApp();

    console.log('应用程序初始化完成');
  } catch (error) {
    console.error('应用程序初始化失败:', error);
    showError('应用程序初始化失败: ' + error.message);
  }
});

/**
 * 初始化应用程序
 */
async function initializeApp() {
  // 初始化视图管理器
  window.viewManager.initialize();

  // 设置默认日期
  setDefaultDate();

  // 绑定事件监听器
  bindEventListeners();

  // 加载初始数据
  await loadInitialData();

  // 显示仪表盘
  window.stateManager.setState('currentView', 'dashboard');
}

/**
 * 设置默认日期
 */
function setDefaultDate() {
  const today = new Date().toISOString().split('T')[0];
  const orderDateElement = document.getElementById('order-date');
  if (orderDateElement) {
    orderDateElement.value = today;
  }
}

/**
 * 绑定事件监听器
 */
function bindEventListeners() {
  // 订单表单相关事件
  bindOrderFormEvents();

  // 订单列表相关事件
  bindOrderListEvents();

  // 仪表盘相关事件
  bindDashboardEvents();
}

/**
 * 绑定订单表单事件
 */
function bindOrderFormEvents() {
  // 添加材料项按钮
  const addItemBtn = document.getElementById('add-item-btn');
  if (addItemBtn) {
    addItemBtn.addEventListener('click', addOrderItem);
  }

  // 保存订单按钮
  const saveOrderBtn = document.getElementById('save-order-btn');
  if (saveOrderBtn) {
    saveOrderBtn.addEventListener('click', saveOrder);
  }

  // 取消按钮
  const cancelOrderBtn = document.getElementById('cancel-order-btn');
  if (cancelOrderBtn) {
    cancelOrderBtn.addEventListener('click', () => {
      window.stateManager.setState('currentView', 'dashboard');
    });
  }
}

/**
 * 绑定订单列表事件
 */
function bindOrderListEvents() {
  // 搜索按钮
  const searchBtn = document.getElementById('search-btn');
  if (searchBtn) {
    searchBtn.addEventListener('click', searchOrders);
  }

  // 导出CSV按钮
  const exportCsvBtn = document.getElementById('export-csv-btn');
  if (exportCsvBtn) {
    exportCsvBtn.addEventListener('click', exportOrdersToCSV);
  }

  // 分页按钮
  const prevPageBtn = document.getElementById('prev-page-btn');
  const nextPageBtn = document.getElementById('next-page-btn');

  if (prevPageBtn) {
    prevPageBtn.addEventListener('click', () => changePage(-1));
  }

  if (nextPageBtn) {
    nextPageBtn.addEventListener('click', () => changePage(1));
  }
}

/**
 * 绑定仪表盘事件
 */
function bindDashboardEvents() {
  // 监听统计数据变化
  window.stateManager.subscribe('statistics', updateDashboardDisplay);

  // 监听订单数据变化
  window.stateManager.subscribe('orders', updateRecentOrdersTable);
}

/**
 * 加载初始数据
 */
async function loadInitialData() {
  try {
    // 显示加载状态
    window.stateManager.setLoading('orders', true);
    window.stateManager.setLoading('statistics', true);

    // 加载订单数据
    const ordersResult = await window.orderService.getAllOrders();
    if (ordersResult.success) {
      window.stateManager.setState('orders', ordersResult.orders);
    } else {
      console.error('加载订单失败:', ordersResult.message);
    }

    // 加载统计数据
    const statsResult = await window.orderService.getTodayStatistics();
    if (statsResult.success) {
      window.stateManager.setState('statistics', {
        todayOrderCount: statsResult.statistics.orderCount,
        todayTotalAmount: statsResult.statistics.totalAmount
      });
    } else {
      console.error('加载统计数据失败:', statsResult.message);
    }

    // 获取总订单数
    const allOrdersResult = await window.orderService.getAllOrders();
    if (allOrdersResult.success) {
      window.stateManager.updateState('statistics', {
        totalOrderCount: allOrdersResult.orders.length
      });
    }

  } catch (error) {
    console.error('加载初始数据失败:', error);
    showError('加载数据失败: ' + error.message);
  } finally {
    // 隐藏加载状态
    window.stateManager.setLoading('orders', false);
    window.stateManager.setLoading('statistics', false);
  }
}

/**
 * 更新仪表盘显示
 */
function updateDashboardDisplay(statistics) {
  if (!statistics) return;

  // 更新统计卡片
  const todayOrderCountEl = document.getElementById('today-order-count');
  const todayTotalAmountEl = document.getElementById('today-total-amount');
  const totalOrderCountEl = document.getElementById('total-order-count');

  if (todayOrderCountEl) {
    todayOrderCountEl.textContent = statistics.todayOrderCount || 0;
  }

  if (todayTotalAmountEl) {
    todayTotalAmountEl.textContent = `¥${(statistics.todayTotalAmount || 0).toFixed(2)}`;
  }

  if (totalOrderCountEl) {
    totalOrderCountEl.textContent = statistics.totalOrderCount || 0;
  }
}

/**
 * 更新最近订单表格
 */
function updateRecentOrdersTable(orders) {
  if (!orders || !Array.isArray(orders)) return;

  const tableBody = document.getElementById('recent-orders-table');
  if (!tableBody) return;

  // 清空表格
  tableBody.innerHTML = '';

  // 获取最近5个订单
  const recentOrders = orders
    .sort((a, b) => new Date(b.date) - new Date(a.date))
    .slice(0, 5);

  if (recentOrders.length === 0) {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td colspan="5" class="px-6 py-4 text-center text-gray-500">
        暂无订单数据
      </td>
    `;
    tableBody.appendChild(row);
    return;
  }

  // 填充表格
  recentOrders.forEach(order => {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td class="px-6 py-4 whitespace-nowrap">${order.id}</td>
      <td class="px-6 py-4 whitespace-nowrap">${order.date}</td>
      <td class="px-6 py-4 whitespace-nowrap">${order.customerName}</td>
      <td class="px-6 py-4 whitespace-nowrap">¥${order.totalAmount.toFixed(2)}</td>
      <td class="px-6 py-4 whitespace-nowrap">
        <button class="text-indigo-600 hover:text-indigo-900 mr-3 edit-order" data-id="${order.id}">
          编辑
        </button>
        <button class="text-red-600 hover:text-red-900 delete-order" data-id="${order.id}">
          删除
        </button>
      </td>
    `;
    tableBody.appendChild(row);
  });

  // 添加编辑和删除按钮的事件监听器
  document.querySelectorAll('.edit-order').forEach(button => {
    button.addEventListener('click', () => editOrder(button.dataset.id));
  });

  document.querySelectorAll('.delete-order').forEach(button => {
    button.addEventListener('click', () => deleteOrder(button.dataset.id));
  });
}

/**
 * 编辑订单
 */
async function editOrder(orderId) {
  try {
    const result = await window.orderService.getOrderById(orderId);
    if (result.success) {
      window.stateManager.setState('currentOrder', result.order);
      window.stateManager.setState('currentView', 'order-form');
    } else {
      showError('获取订单失败: ' + result.message);
    }
  } catch (error) {
    console.error('编辑订单失败:', error);
    showError('编辑订单失败: ' + error.message);
  }
}

/**
 * 删除订单
 */
async function deleteOrder(orderId) {
  if (!window.viewManager.confirm('确定要删除这个订单吗？此操作不可撤销。')) {
    return;
  }

  try {
    const result = await window.orderService.deleteOrder(orderId);
    if (result.success) {
      showSuccess('订单删除成功');
      // 重新加载数据
      await loadInitialData();
    } else {
      showError('删除订单失败: ' + result.message);
    }
  } catch (error) {
    console.error('删除订单失败:', error);
    showError('删除订单失败: ' + error.message);
  }
}

/**
 * 添加订单材料项
 */
function addOrderItem() {
  const container = document.getElementById('order-items-container');
  if (!container) {
    console.error('Order items container not found');
    return;
  }

  const itemRow = document.createElement('div');
  itemRow.className = 'grid grid-cols-12 gap-4 mb-3 items-center p-3 bg-gray-50 rounded-lg border border-gray-200';

  itemRow.innerHTML = `
    <div class="col-span-5">
      <input type="text" class="form-control item-name" placeholder="输入材料名称" required>
    </div>
    <div class="col-span-2">
      <input type="number" class="form-control item-quantity text-center" value="1" min="0" step="0.01" required>
    </div>
    <div class="col-span-2">
      <input type="number" class="form-control item-price text-center" value="0" min="0" step="0.01" required>
    </div>
    <div class="col-span-2 text-right">
      <span class="item-amount text-lg font-semibold text-gray-900">¥0.00</span>
    </div>
    <div class="col-span-1 text-center">
      <button type="button" class="delete-item p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full transition-colors">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
        </svg>
      </button>
    </div>
  `;

  container.appendChild(itemRow);

  // 添加事件监听器
  const quantityInput = itemRow.querySelector('.item-quantity');
  const priceInput = itemRow.querySelector('.item-price');
  const nameInput = itemRow.querySelector('.item-name');
  const deleteButton = itemRow.querySelector('.delete-item');

  // 数量和价格变化时计算金额
  quantityInput.addEventListener('input', () => calculateItemAmount(itemRow));
  priceInput.addEventListener('input', () => calculateItemAmount(itemRow));

  // 材料名称变化时验证
  nameInput.addEventListener('blur', () => validateItemName(itemRow));

  // 删除按钮点击时删除行
  deleteButton.addEventListener('click', () => {
    if (container.children.length > 1) {
      itemRow.remove();
      calculateOrderTotal();
    } else {
      showError('订单至少需要一个材料项');
    }
  });

  // 初始计算金额
  calculateItemAmount(itemRow);
}

/**
 * 验证材料名称
 */
function validateItemName(row) {
  const nameInput = row.querySelector('.item-name');
  const name = nameInput.value.trim();

  // 移除之前的错误样式
  nameInput.classList.remove('border-red-500');

  if (!name) {
    nameInput.classList.add('border-red-500');
    return false;
  }

  return true;
}

/**
 * 计算单个材料项的金额
 */
function calculateItemAmount(row) {
  const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
  const price = parseFloat(row.querySelector('.item-price').value) || 0;
  const amount = quantity * price;

  row.querySelector('.item-amount').textContent = `¥${amount.toFixed(2)}`;

  // 重新计算订单总金额
  calculateOrderTotal();
}

/**
 * 计算订单总金额
 */
function calculateOrderTotal() {
  let total = 0;

  // 遍历所有材料项，累加金额
  document.querySelectorAll('#order-items-container .item-amount').forEach(element => {
    const amountText = element.textContent;
    const amount = parseFloat(amountText.replace('¥', '')) || 0;
    total += amount;
  });

  // 更新总金额显示
  const totalElement = document.getElementById('order-total-amount');
  if (totalElement) {
    totalElement.textContent = `¥${total.toFixed(2)}`;
  }
}

/**
 * 保存订单
 */
async function saveOrder() {
  try {
    // 验证表单
    const validation = validateOrderForm();
    if (!validation.isValid) {
      showError(validation.errors.join('\n'));
      return;
    }

    // 收集表单数据
    const orderData = collectOrderFormData();

    // 显示保存状态
    const saveBtn = document.getElementById('save-order-btn');
    if (saveBtn) {
      saveBtn.disabled = true;
      saveBtn.textContent = '保存中...';
      saveBtn.classList.add('opacity-50', 'cursor-not-allowed');
    }

    // 判断是新建还是更新
    const currentOrder = window.stateManager.getState('currentOrder');
    let result;

    if (currentOrder && currentOrder.id) {
      // 更新现有订单
      orderData.id = currentOrder.id;
      result = await window.orderService.updateOrder(orderData);
    } else {
      // 创建新订单
      result = await window.orderService.createOrder(orderData);
    }

    if (result.success) {
      showSuccess(currentOrder ? '订单更新成功' : '订单创建成功');

      // 重新加载数据
      await loadInitialData();

      // 返回仪表盘
      window.stateManager.setState('currentView', 'dashboard');
    } else {
      showError(result.message);
    }

  } catch (error) {
    console.error('保存订单失败:', error);
    showError('保存订单失败: ' + error.message);
  } finally {
    // 恢复保存按钮状态
    const saveBtn = document.getElementById('save-order-btn');
    if (saveBtn) {
      saveBtn.disabled = false;
      saveBtn.textContent = '保存订单';
      saveBtn.classList.remove('opacity-50', 'cursor-not-allowed');
    }
  }
}

/**
 * 验证订单表单
 */
function validateOrderForm() {
  const errors = [];

  // 验证订单日期
  const orderDate = document.getElementById('order-date');
  if (!orderDate?.value) {
    errors.push('请选择订单日期');
    orderDate?.classList.add('border-red-500');
  } else {
    orderDate?.classList.remove('border-red-500');
  }

  // 验证客户名称
  const customerName = document.getElementById('customer-name');
  if (!customerName?.value?.trim()) {
    errors.push('请输入客户名称');
    customerName?.classList.add('border-red-500');
  } else {
    customerName?.classList.remove('border-red-500');
  }

  // 验证材料项
  const itemRows = document.querySelectorAll('#order-items-container > div');
  let hasValidItems = false;

  itemRows.forEach((row) => {
    const name = row.querySelector('.item-name')?.value?.trim();
    const quantity = parseFloat(row.querySelector('.item-quantity')?.value) || 0;
    const price = parseFloat(row.querySelector('.item-price')?.value) || 0;

    if (name && quantity > 0 && price > 0) {
      hasValidItems = true;
      // 移除错误样式
      row.querySelector('.item-name')?.classList.remove('border-red-500');
      row.querySelector('.item-quantity')?.classList.remove('border-red-500');
      row.querySelector('.item-price')?.classList.remove('border-red-500');
    } else {
      // 添加错误样式
      if (!name) row.querySelector('.item-name')?.classList.add('border-red-500');
      if (quantity <= 0) row.querySelector('.item-quantity')?.classList.add('border-red-500');
      if (price <= 0) row.querySelector('.item-price')?.classList.add('border-red-500');
    }
  });

  if (!hasValidItems) {
    errors.push('请至少添加一个有效的材料项（名称、数量和价格都不能为空）');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 收集订单表单数据
 */
function collectOrderFormData() {
  const orderData = {
    date: document.getElementById('order-date')?.value || '',
    customerName: document.getElementById('customer-name')?.value?.trim() || '',
    customerContact: document.getElementById('customer-contact')?.value?.trim() || '',
    intermediaryName: document.getElementById('intermediary-name')?.value?.trim() || '',
    intermediaryContact: document.getElementById('intermediary-contact')?.value?.trim() || '',
    notes: document.getElementById('order-notes')?.value?.trim() || '',
    items: []
  };

  // 收集材料项数据
  const itemRows = document.querySelectorAll('#order-items-container > div');
  itemRows.forEach(row => {
    const name = row.querySelector('.item-name')?.value?.trim() || '';
    const quantity = parseFloat(row.querySelector('.item-quantity')?.value) || 0;
    const price = parseFloat(row.querySelector('.item-price')?.value) || 0;

    if (name && quantity > 0 && price > 0) {
      orderData.items.push({
        name,
        quantity,
        price,
        amount: quantity * price
      });
    }
  });

  return orderData;
}

/**
 * 重置订单表单
 */
function resetOrderForm() {
  // 清空表单字段
  const fields = ['customer-name', 'customer-contact', 'intermediary-name', 'intermediary-contact', 'order-notes'];
  fields.forEach(fieldId => {
    const field = document.getElementById(fieldId);
    if (field) field.value = '';
  });

  // 清空材料项容器
  const container = document.getElementById('order-items-container');
  if (container) {
    container.innerHTML = '';
    // 添加一个空的材料项
    addOrderItem();
  }

  // 重置总金额
  const totalElement = document.getElementById('order-total-amount');
  if (totalElement) {
    totalElement.textContent = '¥0.00';
  }

  // 更新表单标题
  const title = document.getElementById('order-form-title');
  if (title) {
    title.textContent = '新建订单';
  }
}

/**
 * 搜索订单
 */
async function searchOrders() {
  try {
    const criteria = {
      keyword: document.getElementById('search-keyword')?.value || '',
      startDate: document.getElementById('search-start-date')?.value || '',
      endDate: document.getElementById('search-end-date')?.value || ''
    };

    window.stateManager.setLoading('orders', true);

    const result = await window.orderService.searchOrders(criteria);
    if (result.success) {
      window.stateManager.setState('filteredOrders', result.orders);
      updateOrderListTable(result.orders);
    } else {
      showError('搜索失败: ' + result.message);
    }
  } catch (error) {
    console.error('搜索订单失败:', error);
    showError('搜索失败: ' + error.message);
  } finally {
    window.stateManager.setLoading('orders', false);
  }
}

/**
 * 导出订单为CSV
 */
async function exportOrdersToCSV() {
  try {
    const orders = window.stateManager.getState('filteredOrders') || window.stateManager.getState('orders') || [];

    if (orders.length === 0) {
      showError('没有可导出的订单数据');
      return;
    }

    const result = await window.orderService.exportOrdersToCSV(orders);
    if (result.success) {
      showSuccess(result.message);
    } else {
      showError(result.message);
    }
  } catch (error) {
    console.error('导出CSV失败:', error);
    showError('导出失败: ' + error.message);
  }
}

/**
 * 分页功能
 */
function changePage(direction) {
  const currentPage = window.stateManager.getState('pagination.currentPage') || 1;
  const newPage = currentPage + direction;

  if (newPage < 1) return;

  window.stateManager.setState('pagination.currentPage', newPage);
  // 重新加载数据
  searchOrders();
}

/**
 * 更新订单列表表格
 */
function updateOrderListTable(orders) {
  const tableBody = document.getElementById('orders-table');
  if (!tableBody) return;

  tableBody.innerHTML = '';

  if (orders.length === 0) {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td colspan="6" class="px-6 py-4 text-center text-gray-500">
        暂无订单数据
      </td>
    `;
    tableBody.appendChild(row);
    return;
  }

  orders.forEach(order => {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td class="px-6 py-4 whitespace-nowrap">${order.id}</td>
      <td class="px-6 py-4 whitespace-nowrap">${order.date}</td>
      <td class="px-6 py-4 whitespace-nowrap">${order.customerName}</td>
      <td class="px-6 py-4 whitespace-nowrap">${order.intermediaryName || '-'}</td>
      <td class="px-6 py-4 whitespace-nowrap">¥${order.totalAmount.toFixed(2)}</td>
      <td class="px-6 py-4 whitespace-nowrap">
        <button class="text-indigo-600 hover:text-indigo-900 mr-3 edit-order" data-id="${order.id}">
          编辑
        </button>
        <button class="text-red-600 hover:text-red-900 delete-order" data-id="${order.id}">
          删除
        </button>
      </td>
    `;
    tableBody.appendChild(row);
  });

  // 添加事件监听器
  document.querySelectorAll('.edit-order').forEach(button => {
    button.addEventListener('click', () => editOrder(button.dataset.id));
  });

  document.querySelectorAll('.delete-order').forEach(button => {
    button.addEventListener('click', () => deleteOrder(button.dataset.id));
  });
}

/**
 * 显示错误消息
 */
function showError(message) {
  if (window.viewManager) {
    window.viewManager.showMessage(message, 'error');
  } else {
    alert('错误: ' + message);
  }
}

/**
 * 显示成功消息
 */
function showSuccess(message) {
  if (window.viewManager) {
    window.viewManager.showMessage(message, 'success');
  } else {
    alert('成功: ' + message);
  }
}

/**
 * 显示信息消息
 */
function showInfo(message) {
  if (window.viewManager) {
    window.viewManager.showMessage(message, 'info');
  } else {
    alert('信息: ' + message);
  }
}

// 重构完成 - 旧代码已被新架构替代
// 新的架构使用 StateManager, ViewManager, OrderService 等模块化组件

/**
 * 加载订单到表单（用于编辑）
 */
function loadOrderToForm(order) {
  if (!order) return;

  // 填充基本信息
  const fields = {
    'order-date': order.date,
    'customer-name': order.customerName,
    'customer-contact': order.customerContact || '',
    'intermediary-name': order.intermediaryName || '',
    'intermediary-contact': order.intermediaryContact || '',
    'order-notes': order.notes || ''
  };

  Object.entries(fields).forEach(([id, value]) => {
    const element = document.getElementById(id);
    if (element) element.value = value;
  });

  // 清空并重新填充材料项
  const container = document.getElementById('order-items-container');
  if (container) {
    container.innerHTML = '';

    if (order.items && order.items.length > 0) {
      order.items.forEach(item => {
        addOrderItemWithData(item);
      });
    } else {
      addOrderItem();
    }
  }

  // 更新表单标题
  const title = document.getElementById('order-form-title');
  if (title) {
    title.textContent = '编辑订单';
  }

  // 重新计算总金额
  calculateOrderTotal();
}

/**
 * 添加带数据的订单材料项
 */
function addOrderItemWithData(itemData = {}) {
  const container = document.getElementById('order-items-container');
  if (!container) return;

  const itemRow = document.createElement('div');
  itemRow.className = 'grid grid-cols-12 gap-4 mb-3 items-center p-3 bg-gray-50 rounded-lg border border-gray-200';

  itemRow.innerHTML = `
    <div class="col-span-5">
      <input type="text" class="form-control item-name" placeholder="输入材料名称" value="${itemData.name || ''}" required>
    </div>
    <div class="col-span-2">
      <input type="number" class="form-control item-quantity text-center" value="${itemData.quantity || 1}" min="0" step="0.01" required>
    </div>
    <div class="col-span-2">
      <input type="number" class="form-control item-price text-center" value="${itemData.price || 0}" min="0" step="0.01" required>
    </div>
    <div class="col-span-2 text-right">
      <span class="item-amount text-lg font-semibold text-gray-900">¥${(itemData.amount || 0).toFixed(2)}</span>
    </div>
    <div class="col-span-1 text-center">
      <button type="button" class="delete-item p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full transition-colors">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
        </svg>
      </button>
    </div>
  `;

  container.appendChild(itemRow);

  // 添加事件监听器
  const quantityInput = itemRow.querySelector('.item-quantity');
  const priceInput = itemRow.querySelector('.item-price');
  const nameInput = itemRow.querySelector('.item-name');
  const deleteButton = itemRow.querySelector('.delete-item');

  // 数量和价格变化时计算金额
  quantityInput.addEventListener('input', () => calculateItemAmount(itemRow));
  priceInput.addEventListener('input', () => calculateItemAmount(itemRow));

  // 材料名称变化时验证
  nameInput.addEventListener('blur', () => validateItemName(itemRow));

  // 删除按钮点击时删除行
  deleteButton.addEventListener('click', () => {
    if (container.children.length > 1) {
      itemRow.remove();
      calculateOrderTotal();
    } else {
      showError('订单至少需要一个材料项');
    }
  });

  // 初始计算金额
  calculateItemAmount(itemRow);
}

// 重构完成 - 旧代码已被新架构替代
// 新的架构使用 StateManager, ViewManager, OrderService 等模块化组件
